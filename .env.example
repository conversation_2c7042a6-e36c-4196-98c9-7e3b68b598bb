NODE_ENV="development"

# Postgres
DATABASE_USER=""
DATABASE_PASSWORD=""
DATABASE_DB=""
DATABASE_PORT=""
DATABASE_HOST=""

# Redis
REDIS_PORT=""
REDIS_HOST=""
CACHE_TTL=""

# MinIO
S3_ENDPOINT=""
S3_ROOT_USER=""
S3_ROOT_PASSWORD=""
S3_PORT=""
S3_CONSOLE_PORT=""

# Better Auth
BETTER_AUTH_SECRET=""
BETTER_AUTH_URL=""
NEXT_PUBLIC_BETTER_AUTH_URL=""

GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""

# SMTP
RESEND_API_KEY=""

# Websockets
WEBSOCKET_URL=""
